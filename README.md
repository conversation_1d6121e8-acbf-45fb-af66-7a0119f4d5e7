# RC & FC Alert System for Educational Institution Buses

A comprehensive digital monitoring system that automatically alerts responsible personnel about the expiry of vehicle documents (RC - Registration Certificate and FC - Fitness Certificate) for educational institution buses.

## 🎯 Purpose

To ensure legal compliance, avoid fines, and guarantee student safety by ensuring that only roadworthy buses with valid documents are used for transportation.

## ✨ Features

- **Document Tracking**: Monitors expiry dates for RC and FC of all buses
- **Multi-Level Alerts**: Sends reminders to drivers, admins, and transport officers
- **Dashboard**: Visual overview of all buses and document statuses
- **Logging**: Maintains records of alerts sent and renewals done
- **Mobile Ready**: Responsive interface for quick access
- **User Roles**: Admin, Transport Officer, and Driver access levels
- **Automated Scheduler**: Daily checks for document expiry
- **Email Notifications**: Automated email alerts with HTML templates
- **SMS Integration**: Critical alerts via SMS (configurable)

## 🏗️ System Architecture

- **Frontend**: HTML/CSS/JavaScript with responsive design
- **Backend**: Flask (Python) with SQLAlchemy ORM
- **Database**: SQLite (easily upgradeable to PostgreSQL/MySQL)
- **Scheduler**: Background task for daily expiry checks
- **Notification Engine**: Email/SMS integration for alerts

## 📦 Installation

### Prerequisites

- Python 3.8 or higher
- pip (Python package installer)

### Setup Steps

1. **Clone or download the project**
   ```bash
   cd "RC FC ALERT SYSTEM"
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment variables**
   ```bash
   # Copy the example environment file
   copy .env.example .env
   
   # Edit .env file with your actual configuration
   # Set your email credentials for notifications
   ```

4. **Run the application**
   ```bash
   cd BACKEND
   python app.py
   ```

5. **Access the system**
   - Open your browser and go to: `http://localhost:5000`
   - Default admin credentials:
     - Username: `admin`
     - Password: `admin123`

## 🚀 Usage

### For Administrators

1. **Login** with admin credentials
2. **Add Buses**: Go to Admin Panel → Bus Management
3. **Manage Users**: Create accounts for transport officers and drivers
4. **Monitor Alerts**: View and manage all system alerts
5. **Generate Reports**: Access various reports and analytics

### For Transport Officers

1. **Login** with provided credentials
2. **View Dashboard**: Monitor bus fleet status
3. **Receive Alerts**: Get notifications about expiring documents
4. **Update Records**: Renew documents when needed

### For Drivers

1. **Login** with provided credentials
2. **View Alerts**: See alerts specific to their assigned bus
3. **Check Status**: Monitor document expiry dates

## 📊 Alert System

The system automatically generates alerts at the following intervals:

- **30 days before expiry**: Initial reminder
- **15 days before expiry**: Warning alert
- **7 days before expiry**: High priority warning
- **3 days before expiry**: Critical alert
- **1 day before expiry**: Urgent alert
- **On expiry date**: Critical alert
- **After expiry**: Expired status with escalation

## 🔧 Configuration

### Email Notifications

1. **Gmail Setup** (recommended):
   - Enable 2-factor authentication
   - Generate an app-specific password
   - Update `.env` file with your credentials

2. **Other Email Providers**:
   - Update SMTP settings in `BACKEND/notifications.py`

### SMS Notifications

1. **Configure SMS API**:
   - Sign up with an SMS service provider
   - Update API credentials in `.env` file

### Database

- **Default**: SQLite (file-based, no setup required)
- **Production**: Easily upgradeable to PostgreSQL or MySQL
- **Backup**: Regular database backups recommended

## 📁 Project Structure

```
RC FC ALERT SYSTEM/
├── BACKEND/
│   ├── app.py              # Main Flask application
│   ├── models.py           # Database models
│   ├── scheduler.py        # Alert scheduler
│   └── notifications.py    # Email/SMS notifications
├── FRONT END/
│   ├── login.html          # Login page
│   ├── dashboard.html      # Main dashboard
│   ├── admin.html          # Admin panel
│   ├── style.css           # Styling
│   └── cert.js             # JavaScript functionality
├── requirements.txt        # Python dependencies
├── .env.example           # Environment variables template
└── README.md              # This file
```

## 🔒 Security Features

- **Password Hashing**: Secure password storage
- **Session Management**: Secure user sessions
- **Role-Based Access**: Different access levels for different users
- **Input Validation**: Protection against common attacks
- **Audit Logging**: Track all user actions

## 🛠️ Customization

### Adding New Document Types

1. Update the `Document` model in `models.py`
2. Modify the frontend forms to include new document types
3. Update the alert logic in `scheduler.py`

### Changing Alert Intervals

1. Edit the `alert_thresholds` in `scheduler.py`
2. Customize alert messages and priorities

### Styling

1. Modify `FRONT END/style.css` for visual changes
2. Update HTML templates for layout changes

## 📱 Mobile Support

The system is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile phones
- All modern web browsers

## 🔄 Backup and Maintenance

### Database Backup

```bash
# Backup SQLite database
copy BACKEND\rc_fc_alert.db backup_folder\
```

### Log Monitoring

- Check application logs for errors
- Monitor alert delivery status
- Review user activity logs

## 🆘 Troubleshooting

### Common Issues

1. **Email not sending**:
   - Check email credentials in `.env`
   - Verify internet connection
   - Check spam folder

2. **Database errors**:
   - Ensure write permissions in BACKEND folder
   - Check database file integrity

3. **Scheduler not running**:
   - Verify the application is running continuously
   - Check for Python errors in console

### Support

For technical support or feature requests:
1. Check the troubleshooting section
2. Review application logs
3. Contact your system administrator

## 📈 Future Enhancements

- **Mobile App**: Native mobile application
- **WhatsApp Integration**: Notifications via WhatsApp
- **Advanced Analytics**: Detailed reporting and insights
- **Document Upload**: File attachment for certificates
- **GPS Integration**: Real-time bus tracking
- **Multi-language Support**: Localization options

## 📄 License

This project is developed for educational institutions to ensure transportation safety and compliance.

## 🤝 Contributing

To contribute to this project:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

---

**Note**: This system is designed to help educational institutions maintain compliance with vehicle documentation requirements. Always verify with local transportation authorities for specific legal requirements in your area.
