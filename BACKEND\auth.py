"""
Authentication routes and utilities for RC & FC Alert System
"""

from functools import wraps
from flask import Blueprint, request, jsonify, session, redirect, url_for, flash, render_template_string
from models import User, ActionLog
from database import db
import datetime

auth_bp = Blueprint('auth', __name__)

# Login form HTML template
LOGIN_FORM = """
<!DOCTYPE html>
<html>
<head>
    <title>RC & FC Alert System - Login</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 400px; margin: 100px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="password"] { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; width: 100%; }
        button:hover { background-color: #0056b3; }
        .alert { padding: 10px; margin-bottom: 15px; border-radius: 4px; }
        .alert-danger { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    </style>
</head>
<body>
    <h2>RC & FC Alert System</h2>
    <h3>Login</h3>
    
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}
    
    <form method="post">
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" name="username" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" required>
        </div>
        
        <button type="submit">Login</button>
    </form>
    
    <p style="margin-top: 20px; text-align: center; color: #666;">
        Default Admin: admin / admin123
    </p>
</body>
</html>
"""

def login_required(f):
    """Decorator to require login for routes"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

def role_required(required_roles):
    """Decorator to require specific roles for routes"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'user_id' not in session:
                return redirect(url_for('auth.login'))
            
            user = User.query.get(session['user_id'])
            if not user or user.role not in required_roles:
                flash('Access denied. Insufficient permissions.', 'error')
                return redirect(url_for('index'))
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def log_action(action, entity_type=None, entity_id=None, description=None):
    """Log user actions for audit trail"""
    if 'user_id' in session:
        try:
            log_entry = ActionLog(
                user_id=session['user_id'],
                action=action,
                entity_type=entity_type,
                entity_id=entity_id,
                description=description,
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent', '')
            )
            db.session.add(log_entry)
            db.session.commit()
        except Exception as e:
            print(f"Error logging action: {e}")

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """User login route"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if not username or not password:
            flash('Please enter both username and password.', 'error')
            return render_template_string(LOGIN_FORM)
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password) and user.is_active:
            session['user_id'] = user.id
            session['username'] = user.username
            session['role'] = user.role
            session['full_name'] = user.full_name
            
            # Log successful login
            log_action('login', description=f'User {username} logged in successfully')
            
            flash(f'Welcome, {user.full_name}!', 'success')
            return redirect(url_for('index'))
        else:
            # Log failed login attempt
            try:
                log_entry = ActionLog(
                    user_id=None,
                    action='login_failed',
                    description=f'Failed login attempt for username: {username}',
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent', '')
                )
                db.session.add(log_entry)
                db.session.commit()
            except:
                pass
            
            flash('Invalid username or password.', 'error')
    
    return render_template_string(LOGIN_FORM)

@auth_bp.route('/logout')
def logout():
    """User logout route"""
    if 'user_id' in session:
        log_action('logout', description=f'User {session.get("username")} logged out')
    
    session.clear()
    flash('You have been logged out successfully.', 'success')
    return redirect(url_for('auth.login'))

@auth_bp.route('/api/current_user')
@login_required
def current_user():
    """API endpoint to get current user information"""
    user = User.query.get(session['user_id'])
    if user:
        return jsonify(user.to_dict())
    return jsonify({'error': 'User not found'}), 404

def get_current_user():
    """Helper function to get current logged-in user"""
    if 'user_id' in session:
        return User.query.get(session['user_id'])
    return None

def has_permission(required_roles):
    """Check if current user has required permissions"""
    user = get_current_user()
    if not user:
        return False
    return user.role in required_roles
