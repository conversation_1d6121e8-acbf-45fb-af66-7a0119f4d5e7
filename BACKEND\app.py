import os
import datetime
import threading
import time
import requests
from flask import Flask, render_template_string, request, redirect, url_for, flash, jsonify, session
from flask_cors import CORS
from dotenv import load_dotenv
from database import db, init_app
from models import User, Bus, Document, Alert, ActionLog
from auth import auth_bp, login_required, role_required, log_action, get_current_user
from alert_service import alert_service

load_dotenv()

app = Flask(__name__)
app.secret_key = os.environ.get("FLASK_SECRET_KEY", "supersecret")

# Database configuration
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///rc_fc_alert.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
CORS(app)
init_app(app)

# Register blueprints
app.register_blueprint(auth_bp)

SOURCEREST_API_KEY = os.environ.get('SOURCEREST_API_KEY')

FORM_HTML = """
<!DOCTYPE html>
<html>
<head>
    <title>RC & FC Alert System</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .user-info { text-align: right; }
        table { border-collapse: collapse; width: 100%; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .form-group { margin-bottom: 10px; }
        label { display: inline-block; width: 200px; }
        input { padding: 5px; margin-left: 10px; }
        button { background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .logout-btn { background-color: #dc3545; }
        .logout-btn:hover { background-color: #c82333; }
    </style>
</head>
<body>
    <div class="header">
        <h2>RC & FC Alert System</h2>
        <div class="user-info">
            <span>Welcome, {{ current_user.full_name }} ({{ current_user.role }})</span>
            <a href="/logout"><button class="logout-btn">Logout</button></a>
        </div>
    </div>

    <h3>Register Bus</h3>
    {% with messages = get_flashed_messages() %}
      {% if messages %}
        <ul style="color: green;">
          {% for message in messages %}
            <li>{{ message }}</li>
          {% endfor %}
        </ul>
      {% endif %}
    {% endwith %}
    <form method="post">
        <label>Bus Number:</label><input name="bus_number" required><br><br>
        <label>RC Expiry Date:</label><input type="date" name="rc_expiry" required><br><br>
        <label>FC Expiry Date:</label><input type="date" name="fc_expiry" required><br><br>
        <label>Driver Name:</label><input name="driver" required><br><br>
        <label>Driver Phone (+countrycode):</label><input name="driver_phone" required><br><br>
        <label>Owner Name:</label><input name="owner_name"><br><br>
        <label>Owner Phone:</label><input name="owner_phone"><br><br>
        <button type="submit">Register Bus</button>
    </form>
    <hr>
    <h2>All Buses</h2>
    <table border="1">
        <tr><th>Bus</th><th>RC Expiry</th><th>FC Expiry</th><th>Driver</th><th>Phone</th></tr>
        {% for bus in buses %}
        <tr>
            <td>{{bus['bus_number']}}</td>
            <td>{{bus['rc_expiry']}}</td>
            <td>{{bus['fc_expiry']}}</td>
            <td>{{bus['driver']}}</td>
            <td>{{bus['driver_phone']}}</td>
        </tr>
        {% endfor %}
    </table>
    <hr>
    <h2>Alerts Log</h2>
    <ul>
    {% for log in alerts_log %}
        <li>{{log}}</li>
    {% endfor %}
    </ul>
    <hr>
    <h2>Example: Call Gemini AI API with curl</h2>
    <pre>
curl "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=GEMINI_API_KEY" \
  -H 'Content-Type: application/json' \
  -X POST \
  -d '{
    "contents": [
      {
        "parts": [
          {
            "text": "Explain how AI works in a few words"
          }
        ]
      }
    ]
  }'
    </pre>
</body>
</html>
"""

def send_sms(to_number, message):
    if not SOURCEREST_API_KEY:
        print("Sourcerest API key not set. Skipping SMS.")
        return
    try:
        response = requests.post(
            "https://api.sourcerest.com/sms/send",
            headers={
                "Authorization": f"Bearer {SOURCEREST_API_KEY}",
                "Content-Type": "application/json"
            },
            json={
                "to": to_number,
                "message": message
            }
        )
        if response.status_code == 200:
            print(f"SMS sent to {to_number}")
        else:
            print(f"SMS send failed: {response.text}")
    except Exception as e:
        print(f"SMS send failed: {e}")

def check_and_alert():
    """Background task to check document expiry and send alerts"""
    with app.app_context():
        while True:
            today = datetime.date.today()

            # Get all active buses with their documents
            buses = Bus.query.filter_by(is_active=True).all()

            for bus in buses:
                documents = Document.query.filter_by(bus_id=bus.id).all()

                for document in documents:
                    days_left = document.days_to_expiry()

                    # Check if we need to send an alert for specific intervals
                    for interval in [30, 15, 7, 1, 0]:
                        if days_left == interval:
                            # Check if alert already sent for this interval
                            existing_alert = Alert.query.filter_by(
                                bus_id=bus.id,
                                document_id=document.id,
                                alert_type='expiry_warning'
                            ).filter(
                                Alert.created_at >= today
                            ).first()

                            if not existing_alert:
                                message = f"{document.document_type.upper()} for Bus {bus.bus_number} expires on {document.expiry_date}.\nPlease renew promptly."
                                send_sms(bus.driver_phone, message)

                                # Create alert record
                                alert = Alert(
                                    bus_id=bus.id,
                                    document_id=document.id,
                                    recipient_id=1,  # Default admin user
                                    alert_type='expiry_warning',
                                    message=message,
                                    priority='high' if days_left <= 7 else 'medium',
                                    notification_method='sms'
                                )
                                alert.mark_as_sent()
                                db.session.add(alert)
                                db.session.commit()

            time.sleep(3600)  # Check every hour instead of every minute

@app.route('/', methods=['GET', 'POST'])
@login_required
def index():
    if request.method == 'POST':
        try:
            current_user = get_current_user()
            user_id = current_user.id if current_user else 1

            # Create new bus record
            bus = Bus(
                bus_number=request.form['bus_number'],
                driver_name=request.form['driver'],
                driver_phone=request.form['driver_phone'],
                owner_name=request.form.get('owner_name', request.form['driver']),
                owner_phone=request.form.get('owner_phone', request.form['driver_phone']),
                created_by=user_id
            )
            db.session.add(bus)
            db.session.flush()  # Get the bus ID

            # Create RC document
            rc_doc = Document(
                bus_id=bus.id,
                document_type='RC',
                document_number=f"RC-{bus.bus_number}",
                expiry_date=datetime.datetime.strptime(request.form['rc_expiry'], '%Y-%m-%d').date(),
                created_by=user_id
            )
            db.session.add(rc_doc)

            # Create FC document
            fc_doc = Document(
                bus_id=bus.id,
                document_type='FC',
                document_number=f"FC-{bus.bus_number}",
                expiry_date=datetime.datetime.strptime(request.form['fc_expiry'], '%Y-%m-%d').date(),
                created_by=user_id
            )
            db.session.add(fc_doc)

            db.session.commit()

            # Log the action
            log_action('add_bus', 'bus', bus.id, f'Added bus {bus.bus_number}')

            flash("Bus registered successfully!")
        except Exception as e:
            db.session.rollback()
            flash(f"Error registering bus: {str(e)}")

        return redirect(url_for('index'))

    # Get all buses and recent alerts for display
    buses = Bus.query.filter_by(is_active=True).all()
    recent_alerts = Alert.query.order_by(Alert.created_at.desc()).limit(10).all()

    # Convert to format expected by template
    buses_data = []
    for bus in buses:
        rc_doc = Document.query.filter_by(bus_id=bus.id, document_type='RC').first()
        fc_doc = Document.query.filter_by(bus_id=bus.id, document_type='FC').first()

        buses_data.append({
            'bus_number': bus.bus_number,
            'rc_expiry': rc_doc.expiry_date.strftime('%Y-%m-%d') if rc_doc else 'N/A',
            'fc_expiry': fc_doc.expiry_date.strftime('%Y-%m-%d') if fc_doc else 'N/A',
            'driver': bus.driver_name,
            'driver_phone': bus.driver_phone
        })

    alerts_log = [f"{alert.created_at}: {alert.message}" for alert in recent_alerts]
    current_user = get_current_user()

    return render_template_string(FORM_HTML, buses=buses_data, alerts_log=alerts_log, current_user=current_user)

if __name__ == '__main__':
    with app.app_context():
        # Create database tables
        db.create_all()

        # Create default admin user if not exists
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                full_name='System Administrator',
                role='admin'
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            db.session.commit()
            print("Default admin user created: admin/admin123")

    # Start background alert checking thread
    threading.Thread(target=check_and_alert, daemon=True).start()

    print("RC & FC Alert System starting...")
    print("Access the application at: http://localhost:5000")
    app.run(debug=False, host='0.0.0.0', port=5000)