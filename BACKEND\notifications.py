"""
Notification System for RC & FC Alert System
Handles email and SMS notifications
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
from datetime import datetime
import os
import requests

# Email configuration
SMTP_SERVER = "smtp.gmail.com"
SMTP_PORT = 587
EMAIL_ADDRESS = os.getenv('EMAIL_ADDRESS', '<EMAIL>')
EMAIL_PASSWORD = os.getenv('EMAIL_PASSWORD', 'your-app-password')

# SMS configuration (using a placeholder API)
SMS_API_URL = os.getenv('SMS_API_URL', 'https://api.textlocal.in/send/')
SMS_API_KEY = os.getenv('SMS_API_KEY', 'your-sms-api-key')

def send_email_notification(to_email, subject, message, alert_type='general'):
    """
    Send email notification
    
    Args:
        to_email (str): Recipient email address
        subject (str): Email subject
        message (str): Email message
        alert_type (str): Type of alert for styling
    
    Returns:
        bool: True if email sent successfully, False otherwise
    """
    try:
        # Create message
        msg = MIMEMultipart("alternative")
        msg["Subject"] = subject
        msg["From"] = EMAIL_ADDRESS
        msg["To"] = to_email
        
        # Create HTML content
        html_content = create_email_template(message, alert_type, subject)
        
        # Create plain text version
        text_content = f"""
RC & FC Alert System Notification

{subject}

{message}

---
This is an automated notification from the RC & FC Alert System.
Please do not reply to this email.

Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        # Attach parts
        part1 = MIMEText(text_content, "plain")
        part2 = MIMEText(html_content, "html")
        
        msg.attach(part1)
        msg.attach(part2)
        
        # Create secure connection and send email
        context = ssl.create_default_context()
        with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
            server.starttls(context=context)
            server.login(EMAIL_ADDRESS, EMAIL_PASSWORD)
            server.sendmail(EMAIL_ADDRESS, to_email, msg.as_string())
        
        print(f"Email sent successfully to {to_email}")
        return True
        
    except Exception as e:
        print(f"Error sending email to {to_email}: {str(e)}")
        return False

def create_email_template(message, alert_type, subject):
    """
    Create HTML email template
    
    Args:
        message (str): Alert message
        alert_type (str): Type of alert
        subject (str): Email subject
    
    Returns:
        str: HTML email content
    """
    # Determine color scheme based on alert type
    color_schemes = {
        'expired': {'bg': '#dc3545', 'text': '#ffffff', 'border': '#c82333'},
        'expiry_today': {'bg': '#fd7e14', 'text': '#ffffff', 'border': '#e55a00'},
        'expiry_warning': {'bg': '#ffc107', 'text': '#212529', 'border': '#e0a800'},
        'expiry_reminder': {'bg': '#17a2b8', 'text': '#ffffff', 'border': '#138496'},
        'test_alert': {'bg': '#6c757d', 'text': '#ffffff', 'border': '#5a6268'},
        'general': {'bg': '#007bff', 'text': '#ffffff', 'border': '#0056b3'}
    }
    
    colors = color_schemes.get(alert_type, color_schemes['general'])
    
    html_template = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{subject}</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                line-height: 1.6;
                margin: 0;
                padding: 0;
                background-color: #f4f4f4;
            }}
            .container {{
                max-width: 600px;
                margin: 20px auto;
                background-color: #ffffff;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                overflow: hidden;
            }}
            .header {{
                background-color: {colors['bg']};
                color: {colors['text']};
                padding: 20px;
                text-align: center;
                border-bottom: 3px solid {colors['border']};
            }}
            .header h1 {{
                margin: 0;
                font-size: 24px;
            }}
            .content {{
                padding: 30px;
            }}
            .alert-message {{
                background-color: #f8f9fa;
                border-left: 4px solid {colors['bg']};
                padding: 15px;
                margin: 20px 0;
                border-radius: 4px;
            }}
            .footer {{
                background-color: #f8f9fa;
                padding: 20px;
                text-align: center;
                font-size: 12px;
                color: #6c757d;
                border-top: 1px solid #dee2e6;
            }}
            .timestamp {{
                color: #6c757d;
                font-size: 12px;
                margin-top: 15px;
            }}
            .logo {{
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">🚌 RC & FC Alert System</div>
                <h1>{subject}</h1>
            </div>
            <div class="content">
                <p>Dear User,</p>
                <div class="alert-message">
                    <strong>{message}</strong>
                </div>
                <p>Please take immediate action to ensure compliance and safety of the educational institution's transportation system.</p>
                <p>If you have already renewed the document, please update the system with the new expiry date.</p>
                <div class="timestamp">
                    Notification generated on: {datetime.now().strftime('%Y-%m-%d at %H:%M:%S')}
                </div>
            </div>
            <div class="footer">
                <p>This is an automated notification from the RC & FC Alert System.</p>
                <p>Please do not reply to this email.</p>
                <p>For support, contact your system administrator.</p>
            </div>
        </div>
    </body>
    </html>
    """
    
    return html_template

def send_sms_notification(to_phone, message):
    """
    Send SMS notification
    
    Args:
        to_phone (str): Recipient phone number
        message (str): SMS message
    
    Returns:
        bool: True if SMS sent successfully, False otherwise
    """
    try:
        # Format phone number (remove any non-digit characters)
        phone = ''.join(filter(str.isdigit, to_phone))
        
        # Truncate message to SMS length limit
        sms_message = message[:160] if len(message) > 160 else message
        
        # Prepare SMS data
        data = {
            'apikey': SMS_API_KEY,
            'numbers': phone,
            'message': f"RC&FC Alert: {sms_message}",
            'sender': 'RCFC'
        }
        
        # Send SMS (placeholder implementation)
        # In production, replace with actual SMS service API
        response = requests.post(SMS_API_URL, data=data, timeout=10)
        
        if response.status_code == 200:
            print(f"SMS sent successfully to {to_phone}")
            return True
        else:
            print(f"Failed to send SMS to {to_phone}: {response.text}")
            return False
            
    except Exception as e:
        print(f"Error sending SMS to {to_phone}: {str(e)}")
        return False

def send_whatsapp_notification(to_phone, message):
    """
    Send WhatsApp notification (placeholder for future implementation)
    
    Args:
        to_phone (str): Recipient phone number
        message (str): WhatsApp message
    
    Returns:
        bool: True if message sent successfully, False otherwise
    """
    # Placeholder for WhatsApp Business API integration
    print(f"WhatsApp notification to {to_phone}: {message}")
    return True

def send_push_notification(user_id, title, message, data=None):
    """
    Send push notification to mobile app (placeholder for future implementation)
    
    Args:
        user_id (int): User ID
        title (str): Notification title
        message (str): Notification message
        data (dict): Additional data
    
    Returns:
        bool: True if notification sent successfully, False otherwise
    """
    # Placeholder for Firebase Cloud Messaging or similar service
    print(f"Push notification to user {user_id}: {title} - {message}")
    return True

def test_email_notification():
    """Test email notification function"""
    test_message = "This is a test notification from the RC & FC Alert System."
    return send_email_notification(
        to_email="<EMAIL>",
        subject="Test Alert - RC & FC System",
        message=test_message,
        alert_type="test_alert"
    )

def test_sms_notification():
    """Test SMS notification function"""
    test_message = "Test SMS from RC & FC Alert System"
    return send_sms_notification(
        to_phone="+1234567890",
        message=test_message
    )

# Configuration validation
def validate_notification_config():
    """Validate notification configuration"""
    issues = []
    
    if not EMAIL_ADDRESS or EMAIL_ADDRESS == '<EMAIL>':
        issues.append("Email address not configured")
    
    if not EMAIL_PASSWORD or EMAIL_PASSWORD == 'your-app-password':
        issues.append("Email password not configured")
    
    if not SMS_API_KEY or SMS_API_KEY == 'your-sms-api-key':
        issues.append("SMS API key not configured")
    
    if issues:
        print("Notification configuration issues:")
        for issue in issues:
            print(f"  - {issue}")
        print("Please set the appropriate environment variables.")
    else:
        print("Notification configuration is valid.")
    
    return len(issues) == 0
