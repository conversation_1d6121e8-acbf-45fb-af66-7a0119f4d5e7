"""
Alert Scheduler for RC & FC Alert System
Runs background tasks to check document expiry dates and generate alerts
"""

import threading
import time
import schedule
from datetime import datetime, date, timedelta
from notifications import send_email_notification, send_sms_notification

class AlertScheduler:
    """Background scheduler for checking document expiry and generating alerts"""
    
    def __init__(self, app):
        self.app = app
        self.running = False
        self.thread = None
    
    def start(self):
        """Start the scheduler in a background thread"""
        if not self.running:
            self.running = True
            self.thread = threading.Thread(target=self._run_scheduler, daemon=True)
            self.thread.start()
            print("Alert scheduler started")
    
    def stop(self):
        """Stop the scheduler"""
        self.running = False
        if self.thread:
            self.thread.join()
        print("Alert scheduler stopped")
    
    def _run_scheduler(self):
        """Run the scheduler loop"""
        # Schedule daily checks
        schedule.every().day.at("09:00").do(self._check_document_expiry)
        schedule.every().day.at("18:00").do(self._check_document_expiry)
        
        while self.running:
            schedule.run_pending()
            time.sleep(60)  # Check every minute
    
    def _check_document_expiry(self):
        """Check all documents for expiry and generate alerts"""
        with self.app.app_context():
            try:
                # Import models here to avoid circular imports
                from models import Bus, Document, Alert, User, ActionLog
                from database import db

                print(f"Running document expiry check at {datetime.now()}")

                # Get all active buses and their documents
                buses = Bus.query.filter_by(is_active=True).all()
                
                for bus in buses:
                    for document in bus.documents:
                        self._process_document_alerts(document)
                
                print("Document expiry check completed")
                
            except Exception as e:
                print(f"Error in document expiry check: {str(e)}")
    
    def _process_document_alerts(self, document):
        """Process alerts for a specific document"""
        days_to_expiry = document.days_to_expiry()
        
        # Define alert thresholds
        alert_thresholds = [30, 15, 7, 3, 1, 0, -1]  # Including expired
        
        for threshold in alert_thresholds:
            if days_to_expiry == threshold:
                self._generate_alert(document, threshold)
                break
    
    def _generate_alert(self, document, days_to_expiry):
        """Generate alert for document expiry"""
        # Import models here to avoid circular imports
        from models import Bus, Document, Alert, User, ActionLog
        from database import db

        bus = document.bus
        
        # Determine alert type and priority
        if days_to_expiry < 0:
            alert_type = 'expired'
            priority = 'critical'
            message = f"URGENT: {document.document_type} for bus {bus.bus_number} has EXPIRED on {document.expiry_date.strftime('%Y-%m-%d')}"
        elif days_to_expiry == 0:
            alert_type = 'expiry_today'
            priority = 'critical'
            message = f"CRITICAL: {document.document_type} for bus {bus.bus_number} expires TODAY"
        elif days_to_expiry <= 3:
            alert_type = 'expiry_warning'
            priority = 'high'
            message = f"HIGH PRIORITY: {document.document_type} for bus {bus.bus_number} expires in {days_to_expiry} day(s)"
        elif days_to_expiry <= 7:
            alert_type = 'expiry_warning'
            priority = 'high'
            message = f"WARNING: {document.document_type} for bus {bus.bus_number} expires in {days_to_expiry} days"
        else:
            alert_type = 'expiry_reminder'
            priority = 'medium'
            message = f"REMINDER: {document.document_type} for bus {bus.bus_number} expires in {days_to_expiry} days"
        
        # Get recipients based on alert priority and type
        recipients = self._get_alert_recipients(document, alert_type, priority)
        
        for recipient in recipients:
            # Check if similar alert already exists for today
            existing_alert = Alert.query.filter_by(
                bus_id=bus.id,
                document_id=document.id,
                recipient_id=recipient.id,
                alert_type=alert_type
            ).filter(Alert.created_at >= date.today()).first()
            
            if not existing_alert:
                # Create new alert
                alert = Alert(
                    bus_id=bus.id,
                    document_id=document.id,
                    recipient_id=recipient.id,
                    alert_type=alert_type,
                    message=message,
                    priority=priority,
                    notification_method='email'
                )
                
                db.session.add(alert)
                db.session.commit()
                
                # Send notification
                self._send_notification(alert, recipient)
                
                print(f"Alert generated for {recipient.username}: {message}")
    
    def _get_alert_recipients(self, document, alert_type, priority):
        """Get list of users who should receive the alert"""
        recipients = []
        
        # Always include admin users
        admins = User.query.filter_by(role='admin', is_active=True).all()
        recipients.extend(admins)
        
        # Include transport officers
        transport_officers = User.query.filter_by(role='transport_officer', is_active=True).all()
        recipients.extend(transport_officers)
        
        # For critical alerts, include drivers
        if priority in ['critical', 'high']:
            # Find driver by phone number (simplified approach)
            driver = User.query.filter_by(
                phone=document.bus.driver_phone,
                role='driver',
                is_active=True
            ).first()
            
            if driver:
                recipients.append(driver)
        
        return list(set(recipients))  # Remove duplicates
    
    def _send_notification(self, alert, recipient):
        """Send notification to recipient"""
        try:
            # Send email notification
            if recipient.email:
                email_sent = send_email_notification(
                    to_email=recipient.email,
                    subject=f"Bus Document Alert - {alert.priority.upper()}",
                    message=alert.message,
                    alert_type=alert.alert_type
                )
                
                if email_sent:
                    alert.mark_as_sent()
                    db.session.commit()
            
            # Send SMS for critical alerts
            if alert.priority == 'critical' and recipient.phone:
                sms_sent = send_sms_notification(
                    to_phone=recipient.phone,
                    message=alert.message
                )
                
                if sms_sent:
                    alert.notification_method = 'sms'
                    db.session.commit()
        
        except Exception as e:
            print(f"Error sending notification to {recipient.username}: {str(e)}")
    
    def generate_test_alerts(self):
        """Generate test alerts for demonstration purposes"""
        with self.app.app_context():
            try:
                # Import models here to avoid circular imports
                from models import Bus, Document, Alert, User, ActionLog
                from database import db

                print("Generating test alerts...")

                # Get all documents
                documents = Document.query.all()
                
                for document in documents:
                    # Create a test alert
                    admin = User.query.filter_by(role='admin').first()
                    if admin:
                        alert = Alert(
                            bus_id=document.bus_id,
                            document_id=document.id,
                            recipient_id=admin.id,
                            alert_type='test_alert',
                            message=f"Test alert for {document.document_type} of bus {document.bus.bus_number}",
                            priority='medium',
                            notification_method='email'
                        )
                        
                        db.session.add(alert)
                
                db.session.commit()
                print("Test alerts generated successfully")
                
            except Exception as e:
                print(f"Error generating test alerts: {str(e)}")

def create_scheduler(app):
    """Factory function to create and return scheduler instance"""
    return AlertScheduler(app)

# Manual check function for testing
def manual_expiry_check(app):
    """Manually trigger expiry check for testing"""
    scheduler = AlertScheduler(app)
    scheduler._check_document_expiry()
