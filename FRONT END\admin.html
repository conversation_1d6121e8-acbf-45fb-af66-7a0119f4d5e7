<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RC & FC Alert System - Admin Panel</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .admin-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(16, 24, 32, 0.95);
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 20px #39ff14;
            border: 2px solid #39ff14;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #39ff14;
            text-shadow: 0 0 10px #39ff14;
            margin: 0;
        }
        
        .nav-buttons {
            display: flex;
            gap: 10px;
        }
        
        .nav-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .nav-btn:hover {
            background: #138496;
        }
        
        .logout-btn {
            background: #dc3545;
        }
        
        .logout-btn:hover {
            background: #c82333;
        }
        
        .tabs {
            display: flex;
            background: rgba(16, 24, 32, 0.95);
            border-radius: 10px 10px 0 0;
            border: 2px solid #39ff14;
            border-bottom: none;
            overflow: hidden;
        }
        
        .tab {
            background: transparent;
            color: #39ff14;
            border: none;
            padding: 15px 25px;
            cursor: pointer;
            border-right: 1px solid #39ff14;
            transition: background 0.2s;
        }
        
        .tab:last-child {
            border-right: none;
        }
        
        .tab.active {
            background: #39ff14;
            color: #101820;
        }
        
        .tab:hover:not(.active) {
            background: rgba(57, 255, 20, 0.1);
        }
        
        .tab-content {
            background: rgba(16, 24, 32, 0.95);
            border: 2px solid #39ff14;
            border-top: none;
            border-radius: 0 0 10px 10px;
            padding: 30px;
            min-height: 500px;
        }
        
        .tab-pane {
            display: none;
        }
        
        .tab-pane.active {
            display: block;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #39ff14;
            text-shadow: 0 0 3px #39ff14;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1.5px solid #39ff14;
            border-radius: 4px;
            background: #181f2a;
            color: #39ff14;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #fff;
            box-shadow: 0 0 10px #39ff14;
        }
        
        .btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        
        .btn:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .data-table th, .data-table td {
            border: 1px solid #39ff14;
            padding: 10px;
            text-align: left;
            color: #39ff14;
        }
        
        .data-table th {
            background: rgba(57, 255, 20, 0.2);
            font-weight: bold;
        }
        
        .data-table tr:nth-child(even) {
            background: rgba(57, 255, 20, 0.05);
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-valid { background: #28a745; color: white; }
        .status-attention { background: #17a2b8; color: white; }
        .status-warning { background: #ffc107; color: #212529; }
        .status-critical { background: #fd7e14; color: white; }
        .status-expired { background: #dc3545; color: white; }
        
        .message {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: none;
        }
        
        .message.success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
            color: #28a745;
        }
        
        .message.error {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid #dc3545;
            color: #dc3545;
        }
        
        @media (max-width: 768px) {
            .tabs {
                flex-direction: column;
            }
            
            .tab {
                border-right: none;
                border-bottom: 1px solid #39ff14;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="header">
            <h1>🔧 Admin Panel - RC & FC Alert System</h1>
            <div class="nav-buttons">
                <a href="/dashboard" class="nav-btn">Dashboard</a>
                <button class="nav-btn logout-btn" onclick="logout()">Logout</button>
            </div>
        </div>
        
        <div class="tabs">
            <button class="tab active" onclick="showTab('buses')">Bus Management</button>
            <button class="tab" onclick="showTab('users')">User Management</button>
            <button class="tab" onclick="showTab('alerts')">Alert Management</button>
            <button class="tab" onclick="showTab('reports')">Reports</button>
        </div>
        
        <div class="tab-content">
            <!-- Bus Management Tab -->
            <div id="buses" class="tab-pane active">
                <h2 style="color: #39ff14; margin-bottom: 20px;">Bus Management</h2>
                
                <div id="busMessage" class="message"></div>
                
                <form id="busForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="busNumber">Bus Number:</label>
                            <input type="text" id="busNumber" name="busNumber" required>
                        </div>
                        <div class="form-group">
                            <label for="driverName">Driver Name:</label>
                            <input type="text" id="driverName" name="driverName" required>
                        </div>
                        <div class="form-group">
                            <label for="driverPhone">Driver Phone:</label>
                            <input type="tel" id="driverPhone" name="driverPhone" required>
                        </div>
                        <div class="form-group">
                            <label for="ownerName">Owner Name:</label>
                            <input type="text" id="ownerName" name="ownerName" required>
                        </div>
                        <div class="form-group">
                            <label for="ownerPhone">Owner Phone:</label>
                            <input type="tel" id="ownerPhone" name="ownerPhone" required>
                        </div>
                        <div class="form-group">
                            <label for="rcNumber">RC Number:</label>
                            <input type="text" id="rcNumber" name="rcNumber" required>
                        </div>
                        <div class="form-group">
                            <label for="rcExpiry">RC Expiry Date:</label>
                            <input type="date" id="rcExpiry" name="rcExpiry" required>
                        </div>
                        <div class="form-group">
                            <label for="fcNumber">FC Number:</label>
                            <input type="text" id="fcNumber" name="fcNumber" required>
                        </div>
                        <div class="form-group">
                            <label for="fcExpiry">FC Expiry Date:</label>
                            <input type="date" id="fcExpiry" name="fcExpiry" required>
                        </div>
                    </div>
                    <button type="submit" class="btn">Add Bus</button>
                    <button type="button" class="btn btn-danger" onclick="resetBusForm()">Reset</button>
                </form>
                
                <div id="busesTable">
                    <h3 style="color: #39ff14; margin-top: 30px;">Registered Buses</h3>
                    <div id="busesTableContent">Loading...</div>
                </div>
            </div>
            
            <!-- User Management Tab -->
            <div id="users" class="tab-pane">
                <h2 style="color: #39ff14; margin-bottom: 20px;">User Management</h2>
                
                <div id="userMessage" class="message"></div>
                
                <form id="userForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="username">Username:</label>
                            <input type="text" id="username" name="username" required>
                        </div>
                        <div class="form-group">
                            <label for="email">Email:</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="fullName">Full Name:</label>
                            <input type="text" id="fullName" name="fullName" required>
                        </div>
                        <div class="form-group">
                            <label for="phone">Phone:</label>
                            <input type="tel" id="phone" name="phone">
                        </div>
                        <div class="form-group">
                            <label for="role">Role:</label>
                            <select id="role" name="role" required>
                                <option value="">Select Role</option>
                                <option value="admin">Admin</option>
                                <option value="transport_officer">Transport Officer</option>
                                <option value="driver">Driver</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="password">Password:</label>
                            <input type="password" id="password" name="password" required>
                        </div>
                    </div>
                    <button type="submit" class="btn">Add User</button>
                    <button type="button" class="btn btn-danger" onclick="resetUserForm()">Reset</button>
                </form>
                
                <div id="usersTable">
                    <h3 style="color: #39ff14; margin-top: 30px;">System Users</h3>
                    <div id="usersTableContent">Loading...</div>
                </div>
            </div>
            
            <!-- Alert Management Tab -->
            <div id="alerts" class="tab-pane">
                <h2 style="color: #39ff14; margin-bottom: 20px;">Alert Management</h2>
                
                <div style="margin-bottom: 20px;">
                    <button class="btn" onclick="generateTestAlerts()">Generate Test Alerts</button>
                    <button class="btn" onclick="markAllAlertsRead()">Mark All as Read</button>
                </div>
                
                <div id="alertsTable">
                    <h3 style="color: #39ff14;">All Alerts</h3>
                    <div id="alertsTableContent">Loading...</div>
                </div>
            </div>
            
            <!-- Reports Tab -->
            <div id="reports" class="tab-pane">
                <h2 style="color: #39ff14; margin-bottom: 20px;">Reports & Analytics</h2>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="reportType">Report Type:</label>
                        <select id="reportType" name="reportType">
                            <option value="expiry">Document Expiry Report</option>
                            <option value="alerts">Alert Summary</option>
                            <option value="buses">Bus Fleet Report</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="dateRange">Date Range:</label>
                        <select id="dateRange" name="dateRange">
                            <option value="7">Last 7 days</option>
                            <option value="30">Last 30 days</option>
                            <option value="90">Last 90 days</option>
                        </select>
                    </div>
                </div>
                
                <button class="btn" onclick="generateReport()">Generate Report</button>
                
                <div id="reportContent" style="margin-top: 30px;">
                    <p style="color: #39ff14;">Select report type and click "Generate Report" to view data.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab management
        function showTab(tabName) {
            // Hide all tab panes
            document.querySelectorAll('.tab-pane').forEach(pane => {
                pane.classList.remove('active');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab pane
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
            
            // Load data for the selected tab
            if (tabName === 'buses') {
                loadBuses();
            } else if (tabName === 'users') {
                loadUsers();
            } else if (tabName === 'alerts') {
                loadAlerts();
            }
        }
        
        // Load data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadBuses();
            
            // Set up form submissions
            document.getElementById('busForm').addEventListener('submit', handleBusSubmit);
            document.getElementById('userForm').addEventListener('submit', handleUserSubmit);
        });
        
        // Bus management functions
        async function handleBusSubmit(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const busData = {
                bus_number: formData.get('busNumber'),
                driver_name: formData.get('driverName'),
                driver_phone: formData.get('driverPhone'),
                owner_name: formData.get('ownerName'),
                owner_phone: formData.get('ownerPhone'),
                rc_number: formData.get('rcNumber'),
                rc_expiry: formData.get('rcExpiry'),
                fc_number: formData.get('fcNumber'),
                fc_expiry: formData.get('fcExpiry')
            };
            
            try {
                const response = await fetch('/api/buses', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(busData)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showMessage('busMessage', 'Bus added successfully!', 'success');
                    resetBusForm();
                    loadBuses();
                } else {
                    showMessage('busMessage', result.error || 'Failed to add bus', 'error');
                }
            } catch (error) {
                showMessage('busMessage', 'Network error occurred', 'error');
            }
        }
        
        async function loadBuses() {
            try {
                const response = await fetch('/api/buses');
                if (response.ok) {
                    const buses = await response.json();
                    displayBusesTable(buses);
                } else {
                    document.getElementById('busesTableContent').innerHTML = '<p>Failed to load buses</p>';
                }
            } catch (error) {
                document.getElementById('busesTableContent').innerHTML = '<p>Error loading buses</p>';
            }
        }
        
        function displayBusesTable(buses) {
            const container = document.getElementById('busesTableContent');
            
            if (buses.length === 0) {
                container.innerHTML = '<p>No buses registered yet.</p>';
                return;
            }
            
            const table = `
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Bus Number</th>
                            <th>Driver</th>
                            <th>Owner</th>
                            <th>RC Status</th>
                            <th>FC Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${buses.map(bus => {
                            const rcDoc = bus.documents.find(doc => doc.type === 'RC');
                            const fcDoc = bus.documents.find(doc => doc.type === 'FC');
                            
                            return `
                                <tr>
                                    <td>${bus.bus_number}</td>
                                    <td>${bus.driver_name}<br><small>${bus.driver_phone}</small></td>
                                    <td>${bus.owner_name}<br><small>${bus.owner_phone}</small></td>
                                    <td>${rcDoc ? `<span class="status-badge status-${rcDoc.status}">${rcDoc.expiry_date}</span>` : 'N/A'}</td>
                                    <td>${fcDoc ? `<span class="status-badge status-${fcDoc.status}">${fcDoc.expiry_date}</span>` : 'N/A'}</td>
                                    <td>
                                        <button class="btn" onclick="editBus(${bus.id})">Edit</button>
                                        <button class="btn btn-danger" onclick="deleteBus(${bus.id})">Delete</button>
                                    </td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            `;
            
            container.innerHTML = table;
        }
        
        function resetBusForm() {
            document.getElementById('busForm').reset();
        }
        
        // User management functions
        async function handleUserSubmit(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const userData = {
                username: formData.get('username'),
                email: formData.get('email'),
                full_name: formData.get('fullName'),
                phone: formData.get('phone'),
                role: formData.get('role'),
                password: formData.get('password')
            };
            
            try {
                const response = await fetch('/api/users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(userData)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showMessage('userMessage', 'User created successfully!', 'success');
                    resetUserForm();
                    loadUsers();
                } else {
                    showMessage('userMessage', result.error || 'Failed to create user', 'error');
                }
            } catch (error) {
                showMessage('userMessage', 'Network error occurred', 'error');
            }
        }
        
        async function loadUsers() {
            try {
                const response = await fetch('/api/users');
                if (response.ok) {
                    const users = await response.json();
                    displayUsersTable(users);
                } else {
                    document.getElementById('usersTableContent').innerHTML = '<p>Failed to load users</p>';
                }
            } catch (error) {
                document.getElementById('usersTableContent').innerHTML = '<p>Error loading users</p>';
            }
        }
        
        function displayUsersTable(users) {
            const container = document.getElementById('usersTableContent');
            
            if (users.length === 0) {
                container.innerHTML = '<p>No users found.</p>';
                return;
            }
            
            const table = `
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Username</th>
                            <th>Full Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${users.map(user => `
                            <tr>
                                <td>${user.username}</td>
                                <td>${user.full_name}</td>
                                <td>${user.email}</td>
                                <td>${user.phone || 'N/A'}</td>
                                <td>${user.role}</td>
                                <td>${user.is_active ? 'Active' : 'Inactive'}</td>
                                <td>
                                    <button class="btn" onclick="editUser(${user.id})">Edit</button>
                                    <button class="btn btn-danger" onclick="deleteUser(${user.id})">Delete</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            container.innerHTML = table;
        }
        
        function resetUserForm() {
            document.getElementById('userForm').reset();
        }
        
        // Alert management functions
        async function loadAlerts() {
            try {
                const response = await fetch('/api/alerts');
                if (response.ok) {
                    const alerts = await response.json();
                    displayAlertsTable(alerts);
                } else {
                    document.getElementById('alertsTableContent').innerHTML = '<p>Failed to load alerts</p>';
                }
            } catch (error) {
                document.getElementById('alertsTableContent').innerHTML = '<p>Error loading alerts</p>';
            }
        }
        
        function displayAlertsTable(alerts) {
            const container = document.getElementById('alertsTableContent');
            
            if (alerts.length === 0) {
                container.innerHTML = '<p>No alerts found.</p>';
                return;
            }
            
            const table = `
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Type</th>
                            <th>Priority</th>
                            <th>Message</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${alerts.map(alert => `
                            <tr>
                                <td>${new Date(alert.created_at).toLocaleDateString()}</td>
                                <td>${alert.alert_type}</td>
                                <td><span class="status-badge status-${alert.priority}">${alert.priority}</span></td>
                                <td>${alert.message}</td>
                                <td>${alert.is_read ? 'Read' : 'Unread'}</td>
                                <td>
                                    ${!alert.is_read ? `<button class="btn" onclick="markAlertRead(${alert.id})">Mark Read</button>` : ''}
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            container.innerHTML = table;
        }
        
        async function markAlertRead(alertId) {
            try {
                const response = await fetch(`/api/alerts/${alertId}/read`, {
                    method: 'PUT'
                });
                
                if (response.ok) {
                    loadAlerts();
                }
            } catch (error) {
                console.error('Error marking alert as read:', error);
            }
        }
        
        // Utility functions
        function showMessage(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `message ${type}`;
            element.style.display = 'block';
            
            setTimeout(() => {
                element.style.display = 'none';
            }, 5000);
        }
        
        function logout() {
            window.location.href = '/logout';
        }
        
        // Placeholder functions for future implementation
        function editBus(busId) {
            alert('Edit bus functionality will be implemented soon');
        }
        
        function deleteBus(busId) {
            if (confirm('Are you sure you want to delete this bus?')) {
                alert('Delete bus functionality will be implemented soon');
            }
        }
        
        function editUser(userId) {
            alert('Edit user functionality will be implemented soon');
        }
        
        function deleteUser(userId) {
            if (confirm('Are you sure you want to delete this user?')) {
                alert('Delete user functionality will be implemented soon');
            }
        }
        
        function generateTestAlerts() {
            alert('Test alert generation will be implemented soon');
        }
        
        function markAllAlertsRead() {
            alert('Mark all alerts as read functionality will be implemented soon');
        }
        
        function generateReport() {
            alert('Report generation functionality will be implemented soon');
        }
    </script>
</body>
</html>
