"""
Enhanced Alert Service for RC & FC Alert System
Handles multi-level alerts, escalation, and notification routing
"""

from datetime import datetime, date, timedelta
from models import Bus, Document, Alert, User, ActionLog
from database import db
from notifications import send_email_notification, send_sms_notification, send_whatsapp_notification
import os

class AlertService:
    """Main alert service class for managing all alert operations"""
    
    # Alert intervals in days
    ALERT_INTERVALS = [30, 15, 7, 3, 1, 0]  # Days before expiry
    
    # Alert priorities based on days remaining
    PRIORITY_MAPPING = {
        30: 'low',
        15: 'medium', 
        7: 'high',
        3: 'high',
        1: 'critical',
        0: 'critical'
    }
    
    def __init__(self):
        self.sourcerest_api_key = os.environ.get('SOURCEREST_API_KEY')
    
    def check_all_documents(self):
        """Check all documents for expiry and generate alerts"""
        today = date.today()
        alerts_generated = 0
        
        # Get all active buses with their documents
        buses = Bus.query.filter_by(is_active=True).all()
        
        for bus in buses:
            documents = Document.query.filter_by(bus_id=bus.id).all()
            
            for document in documents:
                alerts_generated += self._check_document_expiry(document, today)
        
        return alerts_generated
    
    def _check_document_expiry(self, document, today):
        """Check a single document for expiry and generate alerts if needed"""
        days_to_expiry = document.days_to_expiry()
        alerts_generated = 0
        
        # Check if we need to send alerts for any intervals
        for interval in self.ALERT_INTERVALS:
            if days_to_expiry == interval:
                # Check if alert already sent for this interval today
                existing_alert = Alert.query.filter_by(
                    bus_id=document.bus_id,
                    document_id=document.id,
                    alert_type=self._get_alert_type(interval)
                ).filter(
                    Alert.created_at >= today
                ).first()
                
                if not existing_alert:
                    alert = self._create_alert(document, interval)
                    if alert:
                        self._send_alert_notifications(alert)
                        alerts_generated += 1
        
        return alerts_generated
    
    def _get_alert_type(self, days_remaining):
        """Get alert type based on days remaining"""
        if days_remaining == 0:
            return 'expired'
        elif days_remaining == 1:
            return 'expiry_today'
        elif days_remaining <= 7:
            return 'expiry_warning'
        else:
            return 'expiry_reminder'
    
    def _create_alert(self, document, days_remaining):
        """Create an alert record in the database"""
        try:
            bus = document.bus
            alert_type = self._get_alert_type(days_remaining)
            priority = self.PRIORITY_MAPPING.get(days_remaining, 'medium')
            
            # Create message based on alert type
            if days_remaining == 0:
                message = f"URGENT: {document.document_type} for Bus {bus.bus_number} has EXPIRED today ({document.expiry_date}). Immediate action required!"
            elif days_remaining == 1:
                message = f"CRITICAL: {document.document_type} for Bus {bus.bus_number} expires TOMORROW ({document.expiry_date}). Please renew immediately!"
            else:
                message = f"{document.document_type} for Bus {bus.bus_number} expires in {days_remaining} days ({document.expiry_date}). Please plan for renewal."
            
            # Find appropriate recipients
            recipients = self._get_alert_recipients(bus, alert_type)
            
            alerts_created = []
            for recipient in recipients:
                alert = Alert(
                    bus_id=bus.id,
                    document_id=document.id,
                    recipient_id=recipient.id,
                    alert_type=alert_type,
                    message=message,
                    priority=priority,
                    notification_method='email'  # Default, will be updated based on sending
                )
                
                db.session.add(alert)
                alerts_created.append(alert)
            
            db.session.commit()
            return alerts_created[0] if alerts_created else None
            
        except Exception as e:
            db.session.rollback()
            print(f"Error creating alert: {e}")
            return None
    
    def _get_alert_recipients(self, bus, alert_type):
        """Get list of users who should receive this alert"""
        recipients = []
        
        # Always include admins for critical alerts
        if alert_type in ['expired', 'expiry_today']:
            admins = User.query.filter_by(role='admin', is_active=True).all()
            recipients.extend(admins)
        
        # Include transport officers for all alerts
        transport_officers = User.query.filter_by(role='transport_officer', is_active=True).all()
        recipients.extend(transport_officers)
        
        # Include bus creator/owner
        if bus.creator:
            recipients.append(bus.creator)
        
        # Remove duplicates
        unique_recipients = list({user.id: user for user in recipients}.values())
        
        return unique_recipients
    
    def _send_alert_notifications(self, alert):
        """Send notifications for an alert through multiple channels"""
        try:
            bus = Bus.query.get(alert.bus_id)
            document = Document.query.get(alert.document_id)
            recipient = User.query.get(alert.recipient_id)
            
            if not all([bus, document, recipient]):
                return False
            
            # Prepare notification content
            subject = f"RC & FC Alert: {document.document_type} - Bus {bus.bus_number}"
            
            # Send email notification
            if recipient.email:
                email_sent = send_email_notification(
                    to_email=recipient.email,
                    subject=subject,
                    message=alert.message,
                    alert_type=alert.alert_type
                )
                
                if email_sent:
                    alert.notification_method = 'email'
                    alert.mark_as_sent()
            
            # Send SMS notification for critical alerts
            if alert.priority in ['high', 'critical'] and recipient.phone:
                sms_sent = self._send_sms_via_sourcerest(recipient.phone, alert.message)
                
                if sms_sent:
                    # Create additional alert record for SMS
                    sms_alert = Alert(
                        bus_id=alert.bus_id,
                        document_id=alert.document_id,
                        recipient_id=alert.recipient_id,
                        alert_type=alert.alert_type,
                        message=alert.message,
                        priority=alert.priority,
                        notification_method='sms'
                    )
                    sms_alert.mark_as_sent()
                    db.session.add(sms_alert)
            
            # Send SMS to driver for critical alerts
            if alert.priority == 'critical' and bus.driver_phone:
                driver_message = f"URGENT: {document.document_type} for your bus {bus.bus_number} expires soon. Contact transport office immediately."
                self._send_sms_via_sourcerest(bus.driver_phone, driver_message)
            
            db.session.commit()
            return True
            
        except Exception as e:
            print(f"Error sending alert notifications: {e}")
            db.session.rollback()
            return False
    
    def _send_sms_via_sourcerest(self, phone_number, message):
        """Send SMS using Sourcerest API"""
        if not self.sourcerest_api_key:
            print("Sourcerest API key not configured. Skipping SMS.")
            return False
        
        try:
            import requests
            
            response = requests.post(
                "https://api.sourcerest.com/sms/send",
                headers={
                    "Authorization": f"Bearer {self.sourcerest_api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "to": phone_number,
                    "message": f"RC&FC Alert: {message[:150]}"  # Truncate for SMS
                },
                timeout=10
            )
            
            if response.status_code == 200:
                print(f"SMS sent successfully to {phone_number}")
                return True
            else:
                print(f"SMS send failed: {response.text}")
                return False
                
        except Exception as e:
            print(f"SMS send error: {e}")
            return False
    
    def create_manual_alert(self, bus_id, message, alert_type='manual', priority='medium', recipient_ids=None):
        """Create a manual alert for specific bus and recipients"""
        try:
            bus = Bus.query.get(bus_id)
            if not bus:
                return False
            
            # If no specific recipients, use default recipients
            if not recipient_ids:
                recipients = self._get_alert_recipients(bus, alert_type)
                recipient_ids = [r.id for r in recipients]
            
            alerts_created = []
            for recipient_id in recipient_ids:
                alert = Alert(
                    bus_id=bus_id,
                    recipient_id=recipient_id,
                    alert_type=alert_type,
                    message=message,
                    priority=priority,
                    notification_method='email'
                )
                
                db.session.add(alert)
                alerts_created.append(alert)
            
            db.session.commit()
            
            # Send notifications for each alert
            for alert in alerts_created:
                self._send_alert_notifications(alert)
            
            return True
            
        except Exception as e:
            print(f"Error creating manual alert: {e}")
            db.session.rollback()
            return False
    
    def get_alert_statistics(self, days=30):
        """Get alert statistics for the last N days"""
        since_date = datetime.now() - timedelta(days=days)
        
        stats = {
            'total_alerts': Alert.query.filter(Alert.created_at >= since_date).count(),
            'alerts_by_type': {},
            'alerts_by_priority': {},
            'alerts_sent': Alert.query.filter(Alert.created_at >= since_date, Alert.is_sent == True).count(),
            'alerts_read': Alert.query.filter(Alert.created_at >= since_date, Alert.is_read == True).count()
        }
        
        # Get alerts by type
        alert_types = db.session.query(Alert.alert_type, db.func.count(Alert.id)).filter(
            Alert.created_at >= since_date
        ).group_by(Alert.alert_type).all()
        
        stats['alerts_by_type'] = {alert_type: count for alert_type, count in alert_types}
        
        # Get alerts by priority
        alert_priorities = db.session.query(Alert.priority, db.func.count(Alert.id)).filter(
            Alert.created_at >= since_date
        ).group_by(Alert.priority).all()
        
        stats['alerts_by_priority'] = {priority: count for priority, count in alert_priorities}
        
        return stats

# Global alert service instance
alert_service = AlertService()
