from datetime import datetime, date, timedelta
from werkzeug.security import generate_password_hash, check_password_hash
from database import db

class User(db.Model):
    """User model for authentication and role management"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(15), nullable=True)
    role = db.Column(db.String(20), nullable=False, default='driver')  # admin, transport_officer, driver
    is_active = db.Column(db.Bo<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    created_buses = db.relationship('Bus', foreign_keys='Bus.created_by', backref='creator', lazy='dynamic')
    created_documents = db.relationship('Document', foreign_keys='Document.created_by', backref='creator', lazy='dynamic')
    alerts_received = db.relationship('Alert', backref='recipient', lazy='dynamic')
    
    def set_password(self, password):
        """Set password hash"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check password against hash"""
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self):
        """Convert user to dictionary"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'full_name': self.full_name,
            'phone': self.phone,
            'role': self.role,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class Bus(db.Model):
    """Bus model for vehicle information"""
    __tablename__ = 'buses'
    
    id = db.Column(db.Integer, primary_key=True)
    bus_number = db.Column(db.String(20), unique=True, nullable=False)
    driver_name = db.Column(db.String(100), nullable=False)
    driver_phone = db.Column(db.String(15), nullable=False)
    driver_license = db.Column(db.String(20), nullable=True)
    owner_name = db.Column(db.String(100), nullable=False)
    owner_phone = db.Column(db.String(15), nullable=False)
    owner_address = db.Column(db.Text, nullable=True)
    route_details = db.Column(db.Text, nullable=True)
    seating_capacity = db.Column(db.Integer, nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    documents = db.relationship('Document', backref='bus', lazy='dynamic', cascade='all, delete-orphan')
    alerts = db.relationship('Alert', backref='bus', lazy='dynamic')
    
    def get_document_status(self):
        """Get status of all documents for this bus"""
        status = {}
        for doc in self.documents:
            status[doc.document_type] = {
                'expiry_date': doc.expiry_date.isoformat(),
                'status': doc.get_status(),
                'days_to_expiry': doc.days_to_expiry()
            }
        return status
    
    def to_dict(self):
        """Convert bus to dictionary"""
        return {
            'id': self.id,
            'bus_number': self.bus_number,
            'driver_name': self.driver_name,
            'driver_phone': self.driver_phone,
            'driver_license': self.driver_license,
            'owner_name': self.owner_name,
            'owner_phone': self.owner_phone,
            'owner_address': self.owner_address,
            'route_details': self.route_details,
            'seating_capacity': self.seating_capacity,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'document_status': self.get_document_status()
        }

class Document(db.Model):
    """Document model for RC and FC certificates"""
    __tablename__ = 'documents'
    
    id = db.Column(db.Integer, primary_key=True)
    bus_id = db.Column(db.Integer, db.ForeignKey('buses.id'), nullable=False)
    document_type = db.Column(db.String(10), nullable=False)  # RC or FC
    document_number = db.Column(db.String(50), nullable=False)
    issue_date = db.Column(db.Date, nullable=True)
    expiry_date = db.Column(db.Date, nullable=False)
    issuing_authority = db.Column(db.String(100), nullable=True)
    renewal_fee = db.Column(db.Float, nullable=True)
    is_renewed = db.Column(db.Boolean, default=False)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def days_to_expiry(self):
        """Calculate days remaining until expiry"""
        today = date.today()
        return (self.expiry_date - today).days
    
    def get_status(self):
        """Get document status based on expiry date"""
        days_left = self.days_to_expiry()
        
        if days_left < 0:
            return 'expired'
        elif days_left <= 7:
            return 'critical'
        elif days_left <= 15:
            return 'warning'
        elif days_left <= 30:
            return 'attention'
        else:
            return 'valid'
    
    def to_dict(self):
        """Convert document to dictionary"""
        return {
            'id': self.id,
            'bus_id': self.bus_id,
            'document_type': self.document_type,
            'document_number': self.document_number,
            'issue_date': self.issue_date.isoformat() if self.issue_date else None,
            'expiry_date': self.expiry_date.isoformat(),
            'issuing_authority': self.issuing_authority,
            'renewal_fee': self.renewal_fee,
            'is_renewed': self.is_renewed,
            'days_to_expiry': self.days_to_expiry(),
            'status': self.get_status(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class Alert(db.Model):
    """Alert model for tracking notifications"""
    __tablename__ = 'alerts'
    
    id = db.Column(db.Integer, primary_key=True)
    bus_id = db.Column(db.Integer, db.ForeignKey('buses.id'), nullable=False)
    document_id = db.Column(db.Integer, db.ForeignKey('documents.id'), nullable=True)
    recipient_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    alert_type = db.Column(db.String(20), nullable=False)  # expiry_warning, expired, renewal_reminder
    message = db.Column(db.Text, nullable=False)
    priority = db.Column(db.String(10), default='medium')  # low, medium, high, critical
    is_sent = db.Column(db.Boolean, default=False)
    sent_at = db.Column(db.DateTime, nullable=True)
    is_read = db.Column(db.Boolean, default=False)
    read_at = db.Column(db.DateTime, nullable=True)
    notification_method = db.Column(db.String(20), default='email')  # email, sms, app
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    document = db.relationship('Document', backref='alerts')
    
    def mark_as_sent(self):
        """Mark alert as sent"""
        self.is_sent = True
        self.sent_at = datetime.utcnow()
    
    def mark_as_read(self):
        """Mark alert as read"""
        self.is_read = True
        self.read_at = datetime.utcnow()
    
    def to_dict(self):
        """Convert alert to dictionary"""
        return {
            'id': self.id,
            'bus_id': self.bus_id,
            'document_id': self.document_id,
            'recipient_id': self.recipient_id,
            'alert_type': self.alert_type,
            'message': self.message,
            'priority': self.priority,
            'is_sent': self.is_sent,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'is_read': self.is_read,
            'read_at': self.read_at.isoformat() if self.read_at else None,
            'notification_method': self.notification_method,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class ActionLog(db.Model):
    """Action log model for audit trail"""
    __tablename__ = 'action_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    action = db.Column(db.String(50), nullable=False)  # login, logout, add_bus, update_document, etc.
    entity_type = db.Column(db.String(20), nullable=True)  # bus, document, user, alert
    entity_id = db.Column(db.Integer, nullable=True)
    description = db.Column(db.Text, nullable=True)
    ip_address = db.Column(db.String(45), nullable=True)
    user_agent = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', backref='action_logs')
    
    def to_dict(self):
        """Convert action log to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'action': self.action,
            'entity_type': self.entity_type,
            'entity_id': self.entity_id,
            'description': self.description,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
