<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RC & FC Alert System - Dashboard</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(16, 24, 32, 0.95);
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 20px #39ff14;
            border: 2px solid #39ff14;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #39ff14;
            text-shadow: 0 0 10px #39ff14;
            margin: 0;
        }
        
        .user-info {
            color: #39ff14;
            text-align: right;
        }
        
        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(16, 24, 32, 0.95);
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #39ff14;
            box-shadow: 0 0 15px #39ff14;
            text-align: center;
        }
        
        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: #39ff14;
            text-shadow: 0 0 10px #39ff14;
        }
        
        .stat-label {
            color: #39ff14;
            margin-top: 10px;
            opacity: 0.8;
        }
        
        .expired { color: #dc3545 !important; }
        .critical { color: #fd7e14 !important; }
        .warning { color: #ffc107 !important; }
        
        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }
        
        .buses-section, .alerts-section {
            background: rgba(16, 24, 32, 0.95);
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #39ff14;
            box-shadow: 0 0 15px #39ff14;
        }
        
        .section-title {
            color: #39ff14;
            text-shadow: 0 0 5px #39ff14;
            margin-bottom: 20px;
            border-bottom: 1px solid #39ff14;
            padding-bottom: 10px;
        }
        
        .bus-card {
            background: rgba(24, 31, 42, 0.8);
            border: 1px solid #39ff14;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .bus-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .bus-number {
            font-size: 18px;
            font-weight: bold;
            color: #39ff14;
        }
        
        .document-status {
            display: flex;
            gap: 15px;
            margin-top: 10px;
        }
        
        .doc-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-valid { background: #28a745; color: white; }
        .status-attention { background: #17a2b8; color: white; }
        .status-warning { background: #ffc107; color: #212529; }
        .status-critical { background: #fd7e14; color: white; }
        .status-expired { background: #dc3545; color: white; }
        
        .alert-item {
            background: rgba(24, 31, 42, 0.8);
            border-left: 4px solid #39ff14;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        
        .alert-priority-high { border-left-color: #fd7e14; }
        .alert-priority-critical { border-left-color: #dc3545; }
        
        .alert-message {
            color: #39ff14;
            font-size: 14px;
        }
        
        .alert-time {
            color: #6c757d;
            font-size: 12px;
            margin-top: 5px;
        }
        
        .loading {
            text-align: center;
            color: #39ff14;
            padding: 20px;
        }
        
        .add-bus-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="header">
            <h1>🚌 RC & FC Alert Dashboard</h1>
            <div class="user-info">
                <div id="userInfo">Loading...</div>
                <button class="logout-btn" onclick="logout()">Logout</button>
            </div>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalBuses">-</div>
                <div class="stat-label">Total Buses</div>
            </div>
            <div class="stat-card">
                <div class="stat-number expired" id="expiredDocs">-</div>
                <div class="stat-label">Expired Documents</div>
            </div>
            <div class="stat-card">
                <div class="stat-number critical" id="criticalDocs">-</div>
                <div class="stat-label">Critical (≤7 days)</div>
            </div>
            <div class="stat-card">
                <div class="stat-number warning" id="warningDocs">-</div>
                <div class="stat-label">Warning (≤15 days)</div>
            </div>
        </div>
        
        <div class="content-grid">
            <div class="buses-section">
                <h2 class="section-title">Bus Fleet Status</h2>
                <button class="add-bus-btn" onclick="showAddBusForm()">+ Add New Bus</button>
                <div id="busesContainer" class="loading">Loading buses...</div>
            </div>
            
            <div class="alerts-section">
                <h2 class="section-title">Recent Alerts</h2>
                <div id="alertsContainer" class="loading">Loading alerts...</div>
            </div>
        </div>
    </div>

    <script>
        let currentUser = null;
        
        // Load dashboard data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardData();
            loadBuses();
            loadAlerts();
            
            // Refresh data every 5 minutes
            setInterval(() => {
                loadDashboardData();
                loadBuses();
                loadAlerts();
            }, 300000);
        });
        
        async function loadDashboardData() {
            try {
                const response = await fetch('/api/dashboard/stats');
                if (response.ok) {
                    const data = await response.json();
                    
                    document.getElementById('totalBuses').textContent = data.total_buses;
                    document.getElementById('expiredDocs').textContent = data.expired_documents;
                    document.getElementById('criticalDocs').textContent = data.critical_documents;
                    document.getElementById('warningDocs').textContent = data.warning_documents;
                } else {
                    console.error('Failed to load dashboard stats');
                }
            } catch (error) {
                console.error('Error loading dashboard stats:', error);
            }
        }
        
        async function loadBuses() {
            try {
                const response = await fetch('/api/buses');
                if (response.ok) {
                    const buses = await response.json();
                    displayBuses(buses);
                } else {
                    document.getElementById('busesContainer').innerHTML = '<p>Failed to load buses</p>';
                }
            } catch (error) {
                document.getElementById('busesContainer').innerHTML = '<p>Error loading buses</p>';
            }
        }
        
        function displayBuses(buses) {
            const container = document.getElementById('busesContainer');

            if (buses.length === 0) {
                container.innerHTML = '<p>No buses registered yet.</p>';
                return;
            }

            container.innerHTML = buses.map(bus => {
                const rcDoc = bus.documents.find(doc => doc.type === 'RC');
                const fcDoc = bus.documents.find(doc => doc.type === 'FC');

                return `
                    <div class="bus-card">
                        <div class="bus-header">
                            <div class="bus-number">Bus ${bus.bus_number}</div>
                        </div>
                        <div><strong>Driver:</strong> ${bus.driver_name} (${bus.driver_phone})</div>
                        <div><strong>Owner:</strong> ${bus.owner_name}</div>
                        <div class="document-status">
                            ${rcDoc ? `<span class="doc-badge status-${rcDoc.status}">RC: ${rcDoc.expiry_date}</span>` : ''}
                            ${fcDoc ? `<span class="doc-badge status-${fcDoc.status}">FC: ${fcDoc.expiry_date}</span>` : ''}
                        </div>
                    </div>
                `;
            }).join('');
        }
        
        async function loadAlerts() {
            try {
                const response = await fetch('/api/alerts');
                if (response.ok) {
                    const alerts = await response.json();
                    displayAlerts(alerts.slice(0, 10)); // Show only recent 10 alerts
                } else {
                    document.getElementById('alertsContainer').innerHTML = '<p>Failed to load alerts</p>';
                }
            } catch (error) {
                document.getElementById('alertsContainer').innerHTML = '<p>Error loading alerts</p>';
            }
        }
        
        function displayAlerts(alerts) {
            const container = document.getElementById('alertsContainer');
            
            if (alerts.length === 0) {
                container.innerHTML = '<p>No recent alerts.</p>';
                return;
            }
            
            container.innerHTML = alerts.map(alert => `
                <div class="alert-item alert-priority-${alert.priority}">
                    <div class="alert-message">${alert.message}</div>
                    <div class="alert-time">${new Date(alert.created_at).toLocaleString()}</div>
                </div>
            `).join('');
        }
        
        function showAddBusForm() {
            // Redirect to add bus page or show modal
            window.location.href = '/admin';
        }
        
        function logout() {
            window.location.href = '/logout';
        }
    </script>
</body>
</html>
