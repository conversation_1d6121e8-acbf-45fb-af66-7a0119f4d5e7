<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RC & FC Alert System - Login</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .login-container {
            max-width: 400px;
            margin: 100px auto;
            background: rgba(16, 24, 32, 0.95);
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 30px #39ff14, 0 0 10px #222 inset;
            border: 2px solid #39ff14;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h1 {
            color: #39ff14;
            text-shadow: 0 0 10px #39ff14, 0 0 20px #39ff14;
            margin-bottom: 10px;
            font-size: 28px;
        }
        
        .login-header p {
            color: #39ff14;
            opacity: 0.8;
            margin: 0;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #39ff14;
            text-shadow: 0 0 3px #39ff14;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1.5px solid #39ff14;
            border-radius: 4px;
            background: #181f2a;
            color: #39ff14;
            font-size: 16px;
            box-shadow: 0 0 5px #39ff14 inset;
            outline: none;
            transition: border 0.2s, box-shadow 0.2s;
            box-sizing: border-box;
        }
        
        .form-group input:focus {
            border: 1.5px solid #fff;
            box-shadow: 0 0 10px #39ff14, 0 0 5px #fff inset;
        }
        
        .login-btn {
            width: 100%;
            background: #101820;
            color: #39ff14;
            border: 2px solid #39ff14;
            padding: 12px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            box-shadow: 0 0 10px #39ff14, 0 0 5px #222 inset;
            text-shadow: 0 0 5px #39ff14;
            transition: background 0.2s, color 0.2s, box-shadow 0.2s;
        }
        
        .login-btn:hover {
            background: #39ff14;
            color: #101820;
            box-shadow: 0 0 20px #39ff14, 0 0 10px #fff inset;
        }
        
        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .error-message {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid #dc3545;
            color: #dc3545;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: none;
        }
        
        .success-message {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
            color: #28a745;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: none;
        }
        
        .loading {
            display: none;
            text-align: center;
            color: #39ff14;
            margin-top: 10px;
        }
        
        .system-info {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #39ff14;
            opacity: 0.7;
        }
        
        .system-info p {
            color: #39ff14;
            font-size: 12px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>🚌 RC & FC Alert System</h1>
            <p>Educational Institution Bus Document Management</p>
        </div>
        
        <div id="errorMessage" class="error-message"></div>
        <div id="successMessage" class="success-message"></div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" required autocomplete="username">
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required autocomplete="current-password">
            </div>
            
            <button type="submit" class="login-btn" id="loginBtn">
                Login
            </button>
            
            <div id="loading" class="loading">
                Authenticating...
            </div>
        </form>
        
        <div class="system-info">
            <p><strong>Default Admin Credentials:</strong></p>
            <p>Username: admin</p>
            <p>Password: admin123</p>
            <p><em>Please change default password after first login</em></p>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const loading = document.getElementById('loading');
            const errorMessage = document.getElementById('errorMessage');
            const successMessage = document.getElementById('successMessage');
            
            // Clear previous messages
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';
            
            // Show loading state
            loginBtn.disabled = true;
            loginBtn.textContent = 'Logging in...';
            loading.style.display = 'block';
            
            try {
                const response = await fetch('/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    successMessage.textContent = 'Login successful! Redirecting...';
                    successMessage.style.display = 'block';
                    
                    // Redirect based on user role
                    setTimeout(() => {
                        if (data.role === 'admin') {
                            window.location.href = '/admin';
                        } else {
                            window.location.href = '/dashboard';
                        }
                    }, 1000);
                } else {
                    errorMessage.textContent = data.error || 'Login failed. Please try again.';
                    errorMessage.style.display = 'block';
                }
            } catch (error) {
                errorMessage.textContent = 'Network error. Please check your connection and try again.';
                errorMessage.style.display = 'block';
            } finally {
                // Reset button state
                loginBtn.disabled = false;
                loginBtn.textContent = 'Login';
                loading.style.display = 'none';
            }
        });
        
        // Auto-focus username field
        document.getElementById('username').focus();
        
        // Handle Enter key in password field
        document.getElementById('password').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>
</html>
